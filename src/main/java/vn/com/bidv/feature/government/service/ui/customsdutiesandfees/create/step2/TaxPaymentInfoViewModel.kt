package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase

@HiltViewModel
class TaxPaymentInfoViewModel : ViewModelIBankBase<
        TaxPaymentInfoUiState,
        TaxPaymentInfoViewEvent,
        TaxPaymentInfoSideEffect
        >(
    initialState = TaxPaymentInfoUiState(),
    reducer = TaxPaymentInfoReducer()
) {
    override fun handleEffect(
        sideEffect: TaxPaymentInfoSideEffect,
        onResult: (TaxPaymentInfoViewEvent) -> Unit
    ) {
        super.handleEffect(sideEffect, onResult)
    }
}