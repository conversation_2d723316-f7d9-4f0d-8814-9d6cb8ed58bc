package vn.com.bidv.feature.government.service.data

import vn.com.bidv.feature.government.service.data.governmentservice.apis.GovernmentServiceApi
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListChapterRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListCustomsCurrencyRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListEconomicContentRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListExportImportType
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListInquiryCustomsDutyRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListTaxTypeRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListTreasuryRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListTxnPendingListRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.InquiryCustomsDutyReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPendingListReq
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class GovernmentServiceRepository @Inject constructor(
    private val service: GovernmentServiceApi,
) : BaseRepository() {

    suspend fun getListPendingTransaction(
        request: TxnPendingListReq
    ): NetworkResult<DataListTxnPendingListRes> = launch {
        service.listPending(request)
    }

    suspend fun getPendingTransactionDetail(txnDetailReq: TxnDetailReq): NetworkResult<TxnDetailRes> =
          launch { service.detail(txnDetailReq) }

    suspend fun inquiryTransaction(request: InquiryCustomsDutyReq): NetworkResult<DataListInquiryCustomsDutyRes> =
          launch { service.inquiry(request) }

    suspend fun getListChapterCode(): NetworkResult<DataListChapterRes> = launch {
        service.listChapter()
    }

    suspend fun getListEconomicCode(): NetworkResult<DataListEconomicContentRes> = launch {
        service.listEconomicContent()
    }

    suspend fun getListTaxType(): NetworkResult<DataListTaxTypeRes> = launch {
        service.listTaxType()
    }

    suspend fun getListCustomsCurrency(): NetworkResult<DataListCustomsCurrencyRes> = launch {
        service.listCustomsCurrency()
    }

    suspend fun getListTradeType(): NetworkResult<DataListExportImportType> = launch {
        service.listExportImportType()
    }

    suspend fun getListTreasury(): NetworkResult<DataListTreasuryRes> = launch {
        service.listTreasury()
    }
}
